{"version": 3, "file": "BidiServer.js", "sourceRoot": "", "sources": ["../../../src/bidiMapper/BidiServer.ts"], "names": [], "mappings": "AAAA;;;;;;;;;;;;;;;GAeG;AAKH,OAAO,EAAC,YAAY,EAAC,MAAM,0BAA0B,CAAC;AACtD,OAAO,EAAgB,OAAO,EAAC,MAAM,iBAAiB,CAAC;AACvD,OAAO,EAAC,eAAe,EAAC,MAAM,6BAA6B,CAAC;AAK5D,OAAO,EAAC,gBAAgB,EAAyB,MAAM,uBAAuB,CAAC;AAC/E,OAAO,EAAqB,oBAAoB,EAAC,MAAM,oBAAoB,CAAC;AAC5E,OAAO,EAAC,kBAAkB,EAAC,MAAM,2CAA2C,CAAC;AAC7E,OAAO,EAAC,kBAAkB,EAAC,MAAM,yCAAyC,CAAC;AAC3E,OAAO,EAAC,gBAAgB,EAAC,MAAM,mCAAmC,CAAC;AACnE,OAAO,EAAC,sBAAsB,EAAC,MAAM,6CAA6C,CAAC;AACnF,OAAO,EAAC,cAAc,EAAC,MAAM,qCAAqC,CAAC;AACnE,OAAO,EAAC,oBAAoB,EAAC,MAAM,0CAA0C,CAAC;AAC9E,OAAO,EAAC,YAAY,EAAC,MAAM,kCAAkC,CAAC;AAC9D,OAAO,EACL,YAAY,GAEb,MAAM,mCAAmC,CAAC;AAO3C,MAAM,OAAO,UAAW,SAAQ,YAA6B;IAC3D,aAAa,CAAmC;IAChD,UAAU,CAAgB;IAC1B,iBAAiB,CAAmB;IACpC,aAAa,CAAe;IAE5B,uBAAuB,GAAG,IAAI,sBAAsB,EAAE,CAAC;IACvD,aAAa,GAAG,IAAI,YAAY,EAAE,CAAC;IACnC,qBAAqB,GAAG,IAAI,oBAAoB,EAAE,CAAC;IACnD,mBAAmB,CAAqB;IAExC,OAAO,CAAY;IAEnB,sBAAsB,GAAG,CAAC,OAA6B,EAAE,EAAE;QACzD,KAAK,IAAI,CAAC,iBAAiB,CAAC,cAAc,CAAC,OAAO,CAAC,CAAC,KAAK,CAAC,CAAC,KAAK,EAAE,EAAE;YAClE,IAAI,CAAC,OAAO,EAAE,CAAC,OAAO,CAAC,UAAU,EAAE,KAAK,CAAC,CAAC;QAC5C,CAAC,CAAC,CAAC;IACL,CAAC,CAAC;IAEF,uBAAuB,GAAG,KAAK,EAAE,YAA6B,EAAE,EAAE;QAChE,MAAM,OAAO,GAAG,YAAY,CAAC,OAAO,CAAC;QAErC,IAAI,YAAY,CAAC,WAAW,KAAK,IAAI,EAAE,CAAC;YACtC,OAAO,CAAC,cAAc,CAAC,GAAG,YAAY,CAAC,WAAW,CAAC;QACrD,CAAC;QAED,MAAM,IAAI,CAAC,UAAU,CAAC,WAAW,CAAC,OAAO,CAAC,CAAC;IAC7C,CAAC,CAAC;IAEF,YACE,aAA4B,EAC5B,aAA4B,EAC5B,gBAA2B,EAC3B,YAAoB,EACpB,oBAAyC,EACzC,MAAmC,EACnC,MAAiB;QAEjB,KAAK,EAAE,CAAC;QACR,IAAI,CAAC,OAAO,GAAG,MAAM,CAAC;QACtB,IAAI,CAAC,aAAa,GAAG,IAAI,eAAe,CACtC,IAAI,CAAC,uBAAuB,EAC5B,IAAI,CAAC,OAAO,CACb,CAAC;QACF,IAAI,CAAC,UAAU,GAAG,aAAa,CAAC;QAChC,IAAI,CAAC,UAAU,CAAC,YAAY,CAAC,IAAI,CAAC,sBAAsB,CAAC,CAAC;QAC1D,MAAM,kBAAkB,GAAG,IAAI,kBAAkB,CAAC,gBAAgB,CAAC,CAAC;QACpE,IAAI,CAAC,aAAa,GAAG,IAAI,YAAY,CACnC,IAAI,CAAC,uBAAuB,EAC5B,kBAAkB,CACnB,CAAC;QACF,MAAM,cAAc,GAAG,IAAI,cAAc,CACvC,IAAI,CAAC,aAAa,EAClB,IAAI,CAAC,uBAAuB,EAC5B,gBAAgB,EAChB,MAAM,CACP,CAAC;QACF,MAAM,oBAAoB,GAAG,IAAI,oBAAoB,EAAE,CAAC;QACxD,IAAI,CAAC,mBAAmB,GAAG,IAAI,kBAAkB,CAC/C,IAAI,CAAC,aAAa,EAClB,IAAI,CAAC,uBAAuB,CAC7B,CAAC;QACF,IAAI,CAAC,iBAAiB,GAAG,IAAI,gBAAgB,CAC3C,aAAa,EACb,gBAAgB,EAChB,IAAI,CAAC,aAAa,EAClB,IAAI,CAAC,uBAAuB,EAC5B,IAAI,CAAC,aAAa,EAClB,IAAI,CAAC,qBAAqB,EAC1B,cAAc,EACd,oBAAoB,EACpB,IAAI,CAAC,mBAAmB,EACxB,kBAAkB,EAClB,MAAM,EACN,KAAK,EAAE,OAAsB,EAAE,EAAE;YAC/B,oBAAoB,CAAC,aAAa,GAAG,OAAO,CAAC;YAC7C,gFAAgF;YAChF,MAAM,gBAAgB,CAAC,WAAW,CAChC,qCAAqC,EACrC;gBACE,MAAM,EAAE,OAAO,CAAC,mBAAmB,IAAI,KAAK;aAC7C,CACF,CAAC;YACF,IAAI,gBAAgB,CAClB,aAAa,EACb,gBAAgB,EAChB,YAAY,EACZ,IAAI,CAAC,aAAa,EAClB,IAAI,CAAC,uBAAuB,EAC5B,kBAAkB,EAClB,IAAI,CAAC,aAAa,EAClB,cAAc,EACd,IAAI,CAAC,mBAAmB,EACxB,IAAI,CAAC,qBAAqB,EAC1B,oBAAoB,EACpB,OAAO,EAAE,CAAC,2BAA2B,CAAC,IAAI,KAAK,EAC/C,OAAO,EAAE,uBAAuB,EAChC,MAAM,CACP,CAAC;YAEF,0CAA0C;YAC1C,MAAM,gBAAgB,CAAC,WAAW,CAAC,2BAA2B,EAAE;gBAC9D,QAAQ,EAAE,IAAI;aACf,CAAC,CAAC;YAEH,iDAAiD;YACjD,MAAM,gBAAgB,CAAC,WAAW,CAAC,sBAAsB,EAAE;gBACzD,UAAU,EAAE,IAAI;gBAChB,sBAAsB,EAAE,IAAI;gBAC5B,OAAO,EAAE,IAAI;gBACb,oEAAoE;gBACpE,+BAA+B;gBAC/B,MAAM,EAAE;oBACN;wBACE,IAAI,EAAE,MAAM;wBACZ,OAAO,EAAE,IAAI;qBACd;oBACD,EAAE;iBACH;aACF,CAAC,CAAC;YAEH,MAAM,IAAI,CAAC,uBAAuB,EAAE,CAAC;QACvC,CAAC,EACD,IAAI,CAAC,OAAO,CACb,CAAC;QACF,IAAI,CAAC,aAAa,CAAC,EAAE,yCAA2B,CAAC,EAAC,OAAO,EAAE,KAAK,EAAC,EAAE,EAAE;YACnE,IAAI,CAAC,mBAAmB,CAAC,OAAO,EAAE,KAAK,CAAC,CAAC;QAC3C,CAAC,CAAC,CAAC;QACH,IAAI,CAAC,iBAAiB,CAAC,EAAE,mDAEvB,CAAC,EAAC,OAAO,EAAE,KAAK,EAAC,EAAE,EAAE;YACnB,IAAI,CAAC,mBAAmB,CAAC,OAAO,EAAE,KAAK,CAAC,CAAC;QAC3C,CAAC,CACF,CAAC;IACJ,CAAC;IAED;;OAEG;IACH,MAAM,CAAC,KAAK,CAAC,cAAc,CACzB,aAA4B,EAC5B,aAA4B,EAC5B,gBAA2B,EAC3B,YAAoB,EACpB,MAAmC,EACnC,MAAiB;QAEjB,0EAA0E;QAC1E,sEAAsE;QACtE,2EAA2E;QAC3E,6BAA6B;QAC7B,MAAM,CAAC,EAAC,iBAAiB,EAAC,EAAE,EAAC,WAAW,EAAC,CAAC,GAAG,MAAM,OAAO,CAAC,GAAG,CAAC;YAC7D,gBAAgB,CAAC,WAAW,CAAC,2BAA2B,CAAC;YACzD,gBAAgB,CAAC,WAAW,CAAC,mBAAmB,CAAC;YACjD,mDAAmD;YACnD,gBAAgB,CAAC,WAAW,CAAC,6BAA6B,EAAE;gBAC1D,QAAQ,EAAE,SAAS;gBACnB,aAAa,EAAE,IAAI;aACpB,CAAC;SACH,CAAC,CAAC;QACH,IAAI,oBAAoB,GAAG,SAAS,CAAC;QACrC,KAAK,MAAM,IAAI,IAAI,WAAW,EAAE,CAAC;YAC/B,IACE,IAAI,CAAC,gBAAgB;gBACrB,CAAC,iBAAiB,CAAC,QAAQ,CAAC,IAAI,CAAC,gBAAgB,CAAC,EAClD,CAAC;gBACD,oBAAoB,GAAG,IAAI,CAAC,gBAAgB,CAAC;gBAC7C,MAAM;YACR,CAAC;QACH,CAAC;QAED,MAAM,MAAM,GAAG,IAAI,UAAU,CAC3B,aAAa,EACb,aAAa,EACb,gBAAgB,EAChB,YAAY,EACZ,oBAAoB,EACpB,MAAM,EACN,MAAM,CACP,CAAC;QAEF,OAAO,MAAM,CAAC;IAChB,CAAC;IAED;;OAEG;IACH,mBAAmB,CACjB,YAA8C,EAC9C,KAAa;QAEb,IAAI,CAAC,aAAa,CAAC,GAAG,CAAC,YAAY,EAAE,KAAK,CAAC,CAAC;IAC9C,CAAC;IAED,KAAK;QACH,IAAI,CAAC,UAAU,CAAC,KAAK,EAAE,CAAC;IAC1B,CAAC;IAED,KAAK,CAAC,uBAAuB;QAC3B,MAAM,OAAO,CAAC,GAAG,CACf,IAAI,CAAC,uBAAuB;aACzB,mBAAmB,EAAE;aACrB,GAAG,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,eAAe,EAAE,CAAC,CACnC,CAAC;IACJ,CAAC;CACF"}