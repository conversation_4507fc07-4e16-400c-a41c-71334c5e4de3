const puppeteer = require('puppeteer');
const path = require('path');
const fs = require('fs').promises;

class PDFService {
    constructor() {
        this.browser = null;
    }

    /**
     * Initialize browser instance
     */
    async initBrowser() {
        if (!this.browser) {
            this.browser = await puppeteer.launch({
                headless: true,
                args: ['--no-sandbox', '--disable-setuid-sandbox']
            });
        }
        return this.browser;
    }

    /**
     * Close browser instance
     */
    async closeBrowser() {
        if (this.browser) {
            await this.browser.close();
            this.browser = null;
        }
    }

    /**
     * Generate SF2 PDF report
     * @param {Object} reportData - SF2 report data
     * @returns {Promise<Buffer>} PDF buffer
     */
    async generateSF2PDF(reportData) {
        const browser = await this.initBrowser();
        const page = await browser.newPage();

        try {
            // Generate HTML content for SF2
            const htmlContent = await this.generateSF2HTML(reportData);
            
            // Set content and generate PDF
            await page.setContent(htmlContent, { waitUntil: 'networkidle0' });
            
            const pdfBuffer = await page.pdf({
                format: 'A4',
                landscape: true,
                margin: {
                    top: '0.5in',
                    right: '0.5in',
                    bottom: '0.5in',
                    left: '0.5in'
                },
                printBackground: true
            });

            return pdfBuffer;
        } finally {
            await page.close();
        }
    }

    /**
     * Generate SF4 PDF report
     * @param {Object} reportData - SF4 report data
     * @returns {Promise<Buffer>} PDF buffer
     */
    async generateSF4PDF(reportData) {
        const browser = await this.initBrowser();
        const page = await browser.newPage();

        try {
            // Generate HTML content for SF4
            const htmlContent = await this.generateSF4HTML(reportData);
            
            // Set content and generate PDF
            await page.setContent(htmlContent, { waitUntil: 'networkidle0' });
            
            const pdfBuffer = await page.pdf({
                format: 'A4',
                landscape: false,
                margin: {
                    top: '0.5in',
                    right: '0.5in',
                    bottom: '0.5in',
                    left: '0.5in'
                },
                printBackground: true
            });

            return pdfBuffer;
        } finally {
            await page.close();
        }
    }

    /**
     * Generate HTML content for SF2 report
     * @param {Object} reportData - SF2 report data
     * @returns {Promise<string>} HTML content
     */
    async generateSF2HTML(reportData) {
        const { report_info, sessions, students, statistics } = reportData;

        return `
        <!DOCTYPE html>
        <html>
        <head>
            <meta charset="utf-8">
            <title>SF2 Daily Attendance Report</title>
            <style>
                @page {
                    size: A4 landscape;
                    margin: 0.5in;
                }
                body {
                    font-family: 'Times New Roman', serif;
                    font-size: 10px;
                    margin: 0;
                    padding: 0;
                    line-height: 1.2;
                }
                .header {
                    text-align: center;
                    margin-bottom: 20px;
                    border-bottom: 2px solid #000;
                    padding-bottom: 10px;
                }
                .header h1 {
                    font-size: 14px;
                    margin: 2px 0;
                    font-weight: bold;
                    text-transform: uppercase;
                }
                .header h2 {
                    font-size: 12px;
                    margin: 2px 0;
                    font-weight: bold;
                }
                .header .form-title {
                    font-size: 16px;
                    font-weight: bold;
                    margin: 10px 0 5px 0;
                    text-decoration: underline;
                }
                .school-info {
                    display: flex;
                    justify-content: space-between;
                    margin-bottom: 15px;
                    font-size: 11px;
                }
                .school-info div {
                    flex: 1;
                }
                .info-section {
                    margin-bottom: 15px;
                    font-size: 11px;
                }
                .info-row {
                    display: flex;
                    justify-content: space-between;
                    margin-bottom: 5px;
                    align-items: center;
                }
                .info-box {
                    border: 1px solid #000;
                    padding: 3px 8px;
                    margin: 0 5px;
                    min-width: 100px;
                }
                table {
                    width: 100%;
                    border-collapse: collapse;
                    margin-bottom: 15px;
                    font-size: 9px;
                }
                th, td {
                    border: 1px solid #000;
                    padding: 3px;
                    text-align: center;
                    vertical-align: middle;
                }
                th {
                    background-color: #f0f0f0;
                    font-weight: bold;
                    font-size: 8px;
                }
                .student-name {
                    text-align: left;
                    width: 180px;
                    font-size: 8px;
                    padding-left: 5px;
                }
                .lrn {
                    width: 100px;
                    font-size: 8px;
                }
                .session-col {
                    width: 45px;
                    font-size: 8px;
                }
                .summary-col {
                    width: 30px;
                    font-weight: bold;
                }
                .present { background-color: #90EE90; }
                .late { background-color: #FFE4B5; }
                .absent { background-color: #FFB6C1; }
                .statistics {
                    margin-top: 20px;
                    display: flex;
                    justify-content: space-between;
                    font-size: 11px;
                    border: 1px solid #000;
                    padding: 10px;
                }
                .signature-section {
                    margin-top: 30px;
                    display: flex;
                    justify-content: space-between;
                    font-size: 11px;
                }
                .signature-box {
                    text-align: center;
                    width: 200px;
                    border: 1px solid #000;
                    padding: 10px;
                }
                .signature-line {
                    border-bottom: 1px solid #000;
                    margin-bottom: 5px;
                    height: 40px;
                }
                .deped-logo {
                    font-size: 12px;
                    font-weight: bold;
                }
                .form-number {
                    position: absolute;
                    top: 10px;
                    right: 10px;
                    font-size: 10px;
                    border: 1px solid #000;
                    padding: 5px;
                }
            </style>
        </head>
        <body>
            <div class="form-number">SF2</div>
            <div class="header">
                <div class="deped-logo">DepEd</div>
                <h1>Republic of the Philippines</h1>
                <h1>Department of Education</h1>
                <div class="form-title">School Form 2 (SF2)</div>
                <h2>Daily Attendance Report of Learners</h2>
            </div>

            <div class="school-info">
                <div><strong>School:</strong> <span class="info-box">_________________________</span></div>
                <div><strong>Division:</strong> <span class="info-box">_________________________</span></div>
                <div><strong>Region:</strong> <span class="info-box">_________________________</span></div>
            </div>

            <div class="info-section">
                <div class="info-row">
                    <div><strong>Date:</strong> <span class="info-box">${report_info.date_formatted}</span></div>
                    <div><strong>Grade Level:</strong> <span class="info-box">${report_info.grade_level}</span></div>
                    <div><strong>Section:</strong> <span class="info-box">${report_info.section}</span></div>
                </div>
                <div class="info-row" style="margin-top: 10px;">
                    <div><strong>Teacher:</strong> <span class="info-box">_________________________</span></div>
                    <div><strong>Subject:</strong> <span class="info-box">_________________________</span></div>
                    <div><strong>Room:</strong> <span class="info-box">_________________________</span></div>
                </div>
            </div>

            <table>
                <thead>
                    <tr>
                        <th rowspan="3" class="student-name">LEARNER'S NAME<br/>(Last Name, First Name, Middle Name)</th>
                        <th rowspan="3" class="lrn">LRN</th>
                        <th colspan="${sessions.length}">CLASS SESSIONS</th>
                        <th colspan="4">DAILY SUMMARY</th>
                        <th rowspan="3" style="width: 80px;">REMARKS</th>
                    </tr>
                    <tr>
                        ${sessions.map(session =>
                            `<th class="session-col">${session.subject_code}</th>`
                        ).join('')}
                        <th class="summary-col">P</th>
                        <th class="summary-col">L</th>
                        <th class="summary-col">A</th>
                        <th class="summary-col">%</th>
                    </tr>
                    <tr>
                        ${sessions.map(session =>
                            `<th class="session-col" style="font-size: 7px;">${new Date(session.session_time).toLocaleTimeString('en-US', { hour: '2-digit', minute: '2-digit' })}</th>`
                        ).join('')}
                        <th class="summary-col">Present</th>
                        <th class="summary-col">Late</th>
                        <th class="summary-col">Absent</th>
                        <th class="summary-col">Rate</th>
                    </tr>
                </thead>
                <tbody>
                    ${students.map(student => {
                        const attendanceRate = student.daily_summary.total_sessions > 0
                            ? Math.round(((student.daily_summary.present + student.daily_summary.late) / student.daily_summary.total_sessions) * 100)
                            : 0;
                        return `
                        <tr>
                            <td class="student-name">${student.last_name.toUpperCase()}, ${student.first_name}</td>
                            <td class="lrn">${student.lrn}</td>
                            ${student.sessions.map(session =>
                                `<td class="session-col ${session.status}">${session.status === 'present' ? '✓' : session.status === 'late' ? 'L' : 'X'}</td>`
                            ).join('')}
                            <td class="summary-col">${student.daily_summary.present}</td>
                            <td class="summary-col">${student.daily_summary.late}</td>
                            <td class="summary-col">${student.daily_summary.absent}</td>
                            <td class="summary-col">${attendanceRate}%</td>
                            <td style="width: 80px; font-size: 7px;"></td>
                        </tr>
                        `;
                    }).join('')}
                </tbody>
            </table>

            <div class="statistics">
                <div>
                    <strong>SUMMARY:</strong><br/>
                    Total Students Enrolled: <strong>${statistics.total_students}</strong><br/>
                    Total Class Sessions: <strong>${statistics.total_sessions}</strong><br/>
                    Overall Attendance Rate: <strong>${statistics.attendance_rate}%</strong>
                </div>
                <div>
                    <strong>DAILY TOTALS:</strong><br/>
                    Present: <strong>${statistics.total_present}</strong><br/>
                    Late: <strong>${statistics.total_late}</strong><br/>
                    Absent: <strong>${statistics.total_absent}</strong>
                </div>
            </div>

            <div style="margin-top: 20px; font-size: 10px;">
                <div style="margin-bottom: 10px;">
                    <strong>LEGEND:</strong> ✓ = Present, L = Late, X = Absent
                </div>
                <div style="margin-bottom: 10px;">
                    <strong>INSTRUCTIONS:</strong> This form shall be accomplished daily and submitted to the Principal at the end of each week.
                </div>
            </div>

            <div class="signature-section">
                <div class="signature-box">
                    <div style="margin-bottom: 10px;"><strong>Prepared by:</strong></div>
                    <div class="signature-line"></div>
                    <div>Teacher's Signature over Printed Name</div>
                    <div style="margin-top: 5px; font-size: 9px;">Date: _______________</div>
                </div>
                <div class="signature-box">
                    <div style="margin-bottom: 10px;"><strong>Noted by:</strong></div>
                    <div class="signature-line"></div>
                    <div>Principal's Signature over Printed Name</div>
                    <div style="margin-top: 5px; font-size: 9px;">Date: _______________</div>
                </div>
            </div>

            <div style="margin-top: 20px; font-size: 8px; text-align: center;">
                Generated on: ${report_info.generated_at}
            </div>
        </body>
        </html>
        `;
    }

    /**
     * Generate HTML content for SF4 report
     * @param {Object} reportData - SF4 report data
     * @returns {Promise<string>} HTML content
     */
    async generateSF4HTML(reportData) {
        const { report_info, students, statistics, at_risk_students } = reportData;

        return `
        <!DOCTYPE html>
        <html>
        <head>
            <meta charset="utf-8">
            <title>SF4 School Register</title>
            <style>
                @page {
                    size: A4 portrait;
                    margin: 0.5in;
                }
                body {
                    font-family: 'Times New Roman', serif;
                    font-size: 10px;
                    margin: 0;
                    padding: 0;
                    line-height: 1.2;
                }
                .header {
                    text-align: center;
                    margin-bottom: 20px;
                    border-bottom: 2px solid #000;
                    padding-bottom: 10px;
                }
                .header h1 {
                    font-size: 14px;
                    margin: 2px 0;
                    font-weight: bold;
                    text-transform: uppercase;
                }
                .header h2 {
                    font-size: 12px;
                    margin: 2px 0;
                    font-weight: bold;
                }
                .header .form-title {
                    font-size: 16px;
                    font-weight: bold;
                    margin: 10px 0 5px 0;
                    text-decoration: underline;
                }
                .school-info {
                    display: flex;
                    justify-content: space-between;
                    margin-bottom: 15px;
                    font-size: 11px;
                }
                .school-info div {
                    flex: 1;
                }
                .info-section {
                    margin-bottom: 15px;
                    font-size: 11px;
                }
                .info-row {
                    display: flex;
                    justify-content: space-between;
                    margin-bottom: 5px;
                    align-items: center;
                }
                .info-box {
                    border: 1px solid #000;
                    padding: 3px 8px;
                    margin: 0 5px;
                    min-width: 100px;
                }
                table {
                    width: 100%;
                    border-collapse: collapse;
                    margin-bottom: 15px;
                    font-size: 9px;
                }
                th, td {
                    border: 1px solid #000;
                    padding: 3px;
                    text-align: center;
                    vertical-align: middle;
                }
                th {
                    background-color: #f0f0f0;
                    font-weight: bold;
                    font-size: 8px;
                }
                .student-name {
                    text-align: left;
                    width: 140px;
                    font-size: 8px;
                    padding-left: 5px;
                }
                .lrn {
                    width: 90px;
                    font-size: 8px;
                }
                .attendance-col {
                    width: 35px;
                    font-size: 8px;
                }
                .status-col {
                    width: 60px;
                    font-size: 8px;
                }
                .at-risk {
                    background-color: #FFB6C1;
                }
                .statistics {
                    margin-top: 20px;
                    display: grid;
                    grid-template-columns: 1fr 1fr;
                    gap: 20px;
                    font-size: 11px;
                    border: 1px solid #000;
                    padding: 10px;
                }
                .signature-section {
                    margin-top: 30px;
                    display: flex;
                    justify-content: space-between;
                    font-size: 11px;
                }
                .signature-box {
                    text-align: center;
                    width: 200px;
                    border: 1px solid #000;
                    padding: 10px;
                }
                .signature-line {
                    border-bottom: 1px solid #000;
                    margin-bottom: 5px;
                    height: 40px;
                }
                .deped-logo {
                    font-size: 12px;
                    font-weight: bold;
                }
                .form-number {
                    position: absolute;
                    top: 10px;
                    right: 10px;
                    font-size: 10px;
                    border: 1px solid #000;
                    padding: 5px;
                }
            </style>
        </head>
        <body>
            <div class="form-number">SF4</div>
            <div class="header">
                <div class="deped-logo">DepEd</div>
                <h1>Republic of the Philippines</h1>
                <h1>Department of Education</h1>
                <div class="form-title">School Form 4 (SF4)</div>
                <h2>Monthly Learner's Movement and Attendance</h2>
            </div>

            <div class="school-info">
                <div><strong>School:</strong> <span class="info-box">_________________________</span></div>
                <div><strong>Division:</strong> <span class="info-box">_________________________</span></div>
                <div><strong>Region:</strong> <span class="info-box">_________________________</span></div>
            </div>

            <div class="info-section">
                <div class="info-row">
                    <div><strong>Month/Year:</strong> <span class="info-box">${report_info.month_name} ${report_info.year}</span></div>
                    <div><strong>Grade Level:</strong> <span class="info-box">${report_info.grade_level}</span></div>
                    <div><strong>Section:</strong> <span class="info-box">${report_info.section}</span></div>
                </div>
                <div class="info-row" style="margin-top: 10px;">
                    <div><strong>Teacher:</strong> <span class="info-box">_________________________</span></div>
                    <div><strong>School Year:</strong> <span class="info-box">_________________________</span></div>
                    <div><strong>Track/Strand:</strong> <span class="info-box">_________________________</span></div>
                </div>
            </div>

            <table>
                <thead>
                    <tr>
                        <th rowspan="2" class="student-name">LEARNER'S NAME<br/>(Last Name, First Name, Middle Name)</th>
                        <th rowspan="2" class="lrn">LRN</th>
                        <th rowspan="2" style="width: 40px;">SEX<br/>(M/F)</th>
                        <th colspan="5">MONTHLY ATTENDANCE SUMMARY</th>
                        <th rowspan="2" class="status-col">LEARNER'S STATUS</th>
                        <th rowspan="2" class="status-col">REMARKS</th>
                    </tr>
                    <tr>
                        <th class="attendance-col">DAYS<br/>PRESENT</th>
                        <th class="attendance-col">DAYS<br/>LATE</th>
                        <th class="attendance-col">DAYS<br/>ABSENT</th>
                        <th class="attendance-col">TOTAL<br/>DAYS</th>
                        <th class="attendance-col">ATTENDANCE<br/>RATE %</th>
                    </tr>
                </thead>
                <tbody>
                    ${students.map(student => {
                        const totalDays = student.monthly_attendance.present_days + student.monthly_attendance.late_days + student.monthly_attendance.absent_days;
                        const isAtRisk = student.dropout_flag || student.monthly_attendance.attendance_rate < 50;
                        return `
                        <tr class="${isAtRisk ? 'at-risk' : ''}">
                            <td class="student-name">${student.last_name ? student.last_name.toUpperCase() + ', ' + student.first_name : student.full_name}</td>
                            <td class="lrn">${student.lrn}</td>
                            <td style="width: 40px;">${student.gender || 'M'}</td>
                            <td class="attendance-col">${student.monthly_attendance.present_days}</td>
                            <td class="attendance-col">${student.monthly_attendance.late_days}</td>
                            <td class="attendance-col">${student.monthly_attendance.absent_days}</td>
                            <td class="attendance-col">${totalDays}</td>
                            <td class="attendance-col">${student.monthly_attendance.attendance_rate}%</td>
                            <td class="status-col">${student.enrollment_status}</td>
                            <td class="status-col">${isAtRisk ? 'AT RISK' : ''}</td>
                        </tr>
                        `;
                    }).join('')}
                </tbody>
            </table>

            <div class="statistics">
                <div>
                    <strong>ENROLLMENT SUMMARY:</strong><br/>
                    Total Students Enrolled: <strong>${statistics.total_students}</strong><br/>
                    Active Students: <strong>${statistics.active_students}</strong><br/>
                    Transferred Students: <strong>${statistics.transferred_students}</strong><br/>
                    Graduated Students: <strong>${statistics.graduated_students}</strong><br/>
                    At Risk Students: <strong>${statistics.at_risk_students}</strong>
                </div>
                <div>
                    <strong>ATTENDANCE SUMMARY:</strong><br/>
                    Total Present Days: <strong>${statistics.total_present_days}</strong><br/>
                    Total Late Days: <strong>${statistics.total_late_days}</strong><br/>
                    Total Absent Days: <strong>${statistics.total_absent_days}</strong><br/>
                    Overall Attendance Rate: <strong>${statistics.overall_attendance_rate}%</strong>
                </div>
            </div>

            <div style="margin-top: 20px; font-size: 10px;">
                <div style="margin-bottom: 10px;">
                    <strong>AT-RISK CRITERIA:</strong> Students with attendance rate below 50% or more than 10 absent days
                </div>
                <div style="margin-bottom: 10px;">
                    <strong>LEARNER'S STATUS:</strong> Active, Transferred, Dropped, Graduated
                </div>
                <div style="margin-bottom: 10px;">
                    <strong>INSTRUCTIONS:</strong> This form shall be accomplished monthly and submitted to the Division Office.
                </div>
            </div>

            <div class="signature-section">
                <div class="signature-box">
                    <div style="margin-bottom: 10px;"><strong>Prepared by:</strong></div>
                    <div class="signature-line"></div>
                    <div>Teacher's Signature over Printed Name</div>
                    <div style="margin-top: 5px; font-size: 9px;">Date: _______________</div>
                </div>
                <div class="signature-box">
                    <div style="margin-bottom: 10px;"><strong>Noted by:</strong></div>
                    <div class="signature-line"></div>
                    <div>Principal's Signature over Printed Name</div>
                    <div style="margin-top: 5px; font-size: 9px;">Date: _______________</div>
                </div>
            </div>

            <div style="margin-top: 20px; font-size: 8px; text-align: center;">
                Generated on: ${report_info.generated_at}
            </div>
        </body>
        </html>
        `;
    }
}

module.exports = PDFService;
