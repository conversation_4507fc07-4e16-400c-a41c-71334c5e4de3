const express = require('express');
const router = express.Router();
const { requireAuth, requireRole } = require('../middleware/auth');
const SF2ReportService = require('../services/SF2ReportService');
const SF4ReportService = require('../services/SF4ReportService');
const PDFService = require('../services/PDFService');
const ExcelService = require('../services/ExcelService');

/**
 * GET /reports - Reports Dashboard
 */
router.get('/', requireAuth, async (req, res) => {
    try {
        res.render('reports/dashboard', {
            title: 'Reports Dashboard',
            user: req.user,
            success: req.session.success,
            error: req.session.error
        });

        delete req.session.success;
        delete req.session.error;
    } catch (error) {
        console.error('Error loading reports dashboard:', error);
        res.status(500).render('error', {
            title: 'Error',
            message: 'Failed to load reports dashboard',
            error: error
        });
    }
});

/**
 * GET /reports/sf2 - SF2 Daily Attendance Report Interface
 */
router.get('/sf2', requireAuth, async (req, res) => {
    try {
        // Get available classes/sections for filtering
        const Student = require('../models/Student');
        const Subject = require('../models/Subject');
        
        const students = await Student.findAll();
        const subjects = await Subject.findAll();
        
        // Extract unique grade levels and sections
        const gradeLevels = [...new Set(students.map(s => s.grade_level))].sort();
        const sections = [...new Set(students.map(s => s.section))].sort();

        res.render('reports/sf2', {
            title: 'SF2 Daily Attendance Report',
            user: req.user,
            gradeLevels,
            sections,
            subjects,
            success: req.session.success,
            error: req.session.error
        });

        delete req.session.success;
        delete req.session.error;
    } catch (error) {
        console.error('Error loading SF2 interface:', error);
        res.status(500).render('error', {
            title: 'Error',
            message: 'Failed to load SF2 report interface',
            error: error
        });
    }
});

/**
 * GET /reports/sf4 - SF4 School Register Interface
 */
router.get('/sf4', requireAuth, async (req, res) => {
    try {
        // Get available classes/sections for filtering
        const Student = require('../models/Student');
        
        const students = await Student.findAll();
        
        // Extract unique grade levels and sections
        const gradeLevels = [...new Set(students.map(s => s.grade_level))].sort();
        const sections = [...new Set(students.map(s => s.section))].sort();

        res.render('reports/sf4', {
            title: 'SF4 School Register',
            user: req.user,
            gradeLevels,
            sections,
            success: req.session.success,
            error: req.session.error
        });

        delete req.session.success;
        delete req.session.error;
    } catch (error) {
        console.error('Error loading SF4 interface:', error);
        res.status(500).render('error', {
            title: 'Error',
            message: 'Failed to load SF4 report interface',
            error: error
        });
    }
});

/**
 * POST /reports/sf2/generate - Generate SF2 Report
 */
router.post('/sf2/generate', requireAuth, async (req, res) => {
    try {
        const { date, gradeLevel, section, format = 'pdf' } = req.body;

        if (!date) {
            if (req.xhr || req.headers.accept?.indexOf('json') > -1) {
                return res.status(400).json({
                    success: false,
                    message: 'Date is required'
                });
            }
            req.session.error = 'Date is required';
            return res.redirect('/reports/sf2');
        }

        const sf2Service = new SF2ReportService();
        const reportData = await sf2Service.generateReport({
            date,
            gradeLevel,
            section,
            teacherId: req.user.user_id
        });

        if (format === 'preview') {
            const pdfService = new PDFService();
            const htmlContent = await pdfService.generateSF2HTML(reportData);

            res.render('reports/preview', {
                title: 'SF2 Report Preview',
                user: req.user,
                reportType: 'sf2',
                reportData,
                htmlContent
            });
        } else if (format === 'pdf') {
            const pdfService = new PDFService();
            const pdfBuffer = await pdfService.generateSF2PDF(reportData);

            res.setHeader('Content-Type', 'application/pdf');
            res.setHeader('Content-Disposition', `attachment; filename="SF2_${date}_${gradeLevel || 'All'}_${section || 'All'}.pdf"`);
            res.send(pdfBuffer);
        } else if (format === 'excel') {
            const excelService = new ExcelService();
            const excelBuffer = await excelService.generateSF2Excel(reportData);

            res.setHeader('Content-Type', 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet');
            res.setHeader('Content-Disposition', `attachment; filename="SF2_${date}_${gradeLevel || 'All'}_${section || 'All'}.xlsx"`);
            res.send(excelBuffer);
        } else {
            if (req.xhr || req.headers.accept?.indexOf('json') > -1) {
                return res.json({
                    success: true,
                    data: reportData
                });
            }
            // Default to preview for non-API requests
            const pdfService = new PDFService();
            const htmlContent = await pdfService.generateSF2HTML(reportData);

            res.render('reports/preview', {
                title: 'SF2 Report Preview',
                user: req.user,
                reportType: 'sf2',
                reportData,
                htmlContent
            });
        }
    } catch (error) {
        console.error('Error generating SF2 report:', error);
        
        if (req.xhr || req.headers.accept?.indexOf('json') > -1) {
            return res.status(500).json({
                success: false,
                message: 'Failed to generate SF2 report'
            });
        }
        
        req.session.error = 'Failed to generate SF2 report';
        res.redirect('/reports/sf2');
    }
});

/**
 * POST /reports/sf4/generate - Generate SF4 Report
 */
router.post('/sf4/generate', requireAuth, async (req, res) => {
    try {
        const { month, year, gradeLevel, section, format = 'pdf' } = req.body;

        if (!month || !year) {
            if (req.xhr || req.headers.accept?.indexOf('json') > -1) {
                return res.status(400).json({
                    success: false,
                    message: 'Month and year are required'
                });
            }
            req.session.error = 'Month and year are required';
            return res.redirect('/reports/sf4');
        }

        const sf4Service = new SF4ReportService();
        const reportData = await sf4Service.generateReport({
            month,
            year,
            gradeLevel,
            section,
            teacherId: req.user.user_id
        });

        if (format === 'preview') {
            const pdfService = new PDFService();
            const htmlContent = await pdfService.generateSF4HTML(reportData);

            res.render('reports/preview', {
                title: 'SF4 Report Preview',
                user: req.user,
                reportType: 'sf4',
                reportData,
                htmlContent
            });
        } else if (format === 'pdf') {
            const pdfService = new PDFService();
            const pdfBuffer = await pdfService.generateSF4PDF(reportData);

            res.setHeader('Content-Type', 'application/pdf');
            res.setHeader('Content-Disposition', `attachment; filename="SF4_${year}-${month}_${gradeLevel || 'All'}_${section || 'All'}.pdf"`);
            res.send(pdfBuffer);
        } else if (format === 'excel') {
            const excelService = new ExcelService();
            const excelBuffer = await excelService.generateSF4Excel(reportData);

            res.setHeader('Content-Type', 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet');
            res.setHeader('Content-Disposition', `attachment; filename="SF4_${year}-${month}_${gradeLevel || 'All'}_${section || 'All'}.xlsx"`);
            res.send(excelBuffer);
        } else {
            if (req.xhr || req.headers.accept?.indexOf('json') > -1) {
                return res.json({
                    success: true,
                    data: reportData
                });
            }
            // Default to preview for non-API requests
            const pdfService = new PDFService();
            const htmlContent = await pdfService.generateSF4HTML(reportData);

            res.render('reports/preview', {
                title: 'SF4 Report Preview',
                user: req.user,
                reportType: 'sf4',
                reportData,
                htmlContent
            });
        }
    } catch (error) {
        console.error('Error generating SF4 report:', error);
        
        if (req.xhr || req.headers.accept?.indexOf('json') > -1) {
            return res.status(500).json({
                success: false,
                message: 'Failed to generate SF4 report'
            });
        }
        
        req.session.error = 'Failed to generate SF4 report';
        res.redirect('/reports/sf4');
    }
});

/**
 * POST /reports/sf2/export - Export SF2 Report from Preview
 */
router.post('/sf2/export', requireAuth, async (req, res) => {
    try {
        const { date, gradeLevel, section, format } = req.body;

        if (!date) {
            req.session.error = 'Date is required';
            return res.redirect('/reports/sf2');
        }

        const sf2Service = new SF2ReportService();
        const reportData = await sf2Service.generateReport({
            date,
            gradeLevel,
            section,
            teacherId: req.user.user_id
        });

        if (format === 'pdf') {
            const pdfService = new PDFService();
            const pdfBuffer = await pdfService.generateSF2PDF(reportData);

            res.setHeader('Content-Type', 'application/pdf');
            res.setHeader('Content-Disposition', `attachment; filename="SF2_${date}_${gradeLevel || 'All'}_${section || 'All'}.pdf"`);
            res.send(pdfBuffer);
        } else if (format === 'excel') {
            const excelService = new ExcelService();
            const excelBuffer = await excelService.generateSF2Excel(reportData);

            res.setHeader('Content-Type', 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet');
            res.setHeader('Content-Disposition', `attachment; filename="SF2_${date}_${gradeLevel || 'All'}_${section || 'All'}.xlsx"`);
            res.send(excelBuffer);
        } else {
            req.session.error = 'Invalid export format';
            res.redirect('/reports/sf2');
        }
    } catch (error) {
        console.error('Error exporting SF2 report:', error);
        req.session.error = 'Failed to export SF2 report';
        res.redirect('/reports/sf2');
    }
});

/**
 * POST /reports/sf4/export - Export SF4 Report from Preview
 */
router.post('/sf4/export', requireAuth, async (req, res) => {
    try {
        const { month, year, gradeLevel, section, format } = req.body;

        if (!month || !year) {
            req.session.error = 'Month and year are required';
            return res.redirect('/reports/sf4');
        }

        const sf4Service = new SF4ReportService();
        const reportData = await sf4Service.generateReport({
            month,
            year,
            gradeLevel,
            section,
            teacherId: req.user.user_id
        });

        if (format === 'pdf') {
            const pdfService = new PDFService();
            const pdfBuffer = await pdfService.generateSF4PDF(reportData);

            res.setHeader('Content-Type', 'application/pdf');
            res.setHeader('Content-Disposition', `attachment; filename="SF4_${year}-${month}_${gradeLevel || 'All'}_${section || 'All'}.pdf"`);
            res.send(pdfBuffer);
        } else if (format === 'excel') {
            const excelService = new ExcelService();
            const excelBuffer = await excelService.generateSF4Excel(reportData);

            res.setHeader('Content-Type', 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet');
            res.setHeader('Content-Disposition', `attachment; filename="SF4_${year}-${month}_${gradeLevel || 'All'}_${section || 'All'}.xlsx"`);
            res.send(excelBuffer);
        } else {
            req.session.error = 'Invalid export format';
            res.redirect('/reports/sf4');
        }
    } catch (error) {
        console.error('Error exporting SF4 report:', error);
        req.session.error = 'Failed to export SF4 report';
        res.redirect('/reports/sf4');
    }
});

/**
 * GET /reports/history - Historical Reports Browser
 */
router.get('/history', requireAuth, async (req, res) => {
    try {
        // TODO: Implement historical reports functionality
        res.render('reports/history', {
            title: 'Report History',
            user: req.user,
            reports: [], // Will be populated later
            success: req.session.success,
            error: req.session.error
        });

        delete req.session.success;
        delete req.session.error;
    } catch (error) {
        console.error('Error loading report history:', error);
        res.status(500).render('error', {
            title: 'Error',
            message: 'Failed to load report history',
            error: error
        });
    }
});

/**
 * GET /reports/templates - Report Templates Management
 */
router.get('/templates', requireRole(['admin', 'principal']), async (req, res) => {
    try {
        // TODO: Implement template management functionality
        res.render('reports/templates', {
            title: 'Report Templates',
            user: req.user,
            templates: [], // Will be populated later
            success: req.session.success,
            error: req.session.error
        });

        delete req.session.success;
        delete req.session.error;
    } catch (error) {
        console.error('Error loading report templates:', error);
        res.status(500).render('error', {
            title: 'Error',
            message: 'Failed to load report templates',
            error: error
        });
    }
});

module.exports = router;
