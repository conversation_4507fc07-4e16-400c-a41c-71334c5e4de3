<%- include('../layout', { 
    title: 'Report Preview',
    additionalCSS: `
        <style>
            .preview-container {
                background: white;
                border-radius: 8px;
                box-shadow: 0 2px 4px rgba(0,0,0,0.1);
                margin-bottom: 20px;
            }
            .preview-header {
                background: #f8f9fa;
                border-bottom: 1px solid #dee2e6;
                padding: 20px;
                border-radius: 8px 8px 0 0;
            }
            .preview-content {
                padding: 20px;
                max-height: 70vh;
                overflow-y: auto;
                border: 1px solid #dee2e6;
            }
            .preview-actions {
                background: #f8f9fa;
                border-top: 1px solid #dee2e6;
                padding: 20px;
                border-radius: 0 0 8px 8px;
                display: flex;
                justify-content: space-between;
                align-items: center;
            }
            .btn-export {
                margin-right: 10px;
            }
            .btn-pdf {
                background: #dc3545;
                border-color: #dc3545;
            }
            .btn-pdf:hover {
                background: #c82333;
                border-color: #bd2130;
            }
            .btn-excel {
                background: #28a745;
                border-color: #28a745;
            }
            .btn-excel:hover {
                background: #218838;
                border-color: #1e7e34;
            }
            .report-info {
                font-size: 14px;
                color: #6c757d;
            }
            .report-info strong {
                color: #495057;
            }
            .preview-iframe {
                width: 100%;
                height: 600px;
                border: none;
                background: white;
            }
            .loading-overlay {
                position: absolute;
                top: 0;
                left: 0;
                right: 0;
                bottom: 0;
                background: rgba(255, 255, 255, 0.9);
                display: flex;
                align-items: center;
                justify-content: center;
                z-index: 1000;
            }
            .zoom-controls {
                margin-left: 20px;
            }
            .zoom-controls button {
                margin: 0 2px;
                padding: 5px 10px;
                font-size: 12px;
            }
        </style>
    `
}) %>

<div class="container-fluid">
    <div class="row">
        <div class="col-12">
            <div class="d-flex justify-content-between align-items-center mb-4">
                <h1 class="h3">
                    <i class="fas fa-eye"></i>
                    Report Preview
                </h1>
                <div>
                    <a href="/reports" class="btn btn-outline-secondary">
                        <i class="fas fa-arrow-left"></i>
                        Back to Dashboard
                    </a>
                    <% if (reportType === 'sf2') { %>
                        <a href="/reports/sf2" class="btn btn-outline-primary ml-2">
                            <i class="fas fa-edit"></i>
                            Edit Parameters
                        </a>
                    <% } else if (reportType === 'sf4') { %>
                        <a href="/reports/sf4" class="btn btn-outline-primary ml-2">
                            <i class="fas fa-edit"></i>
                            Edit Parameters
                        </a>
                    <% } %>
                </div>
            </div>

            <div class="preview-container">
                <div class="preview-header">
                    <div class="row">
                        <div class="col-md-8">
                            <h4 class="mb-3">
                                <% if (reportType === 'sf2') { %>
                                    <i class="fas fa-calendar-day text-primary"></i>
                                    SF2 Daily Attendance Report
                                <% } else if (reportType === 'sf4') { %>
                                    <i class="fas fa-book text-info"></i>
                                    SF4 School Register
                                <% } %>
                            </h4>
                            <div class="report-info">
                                <% if (reportType === 'sf2') { %>
                                    <strong>Date:</strong> <%= reportData.report_info.date_formatted %><br/>
                                    <strong>Grade Level:</strong> <%= reportData.report_info.grade_level || 'All Levels' %><br/>
                                    <strong>Section:</strong> <%= reportData.report_info.section || 'All Sections' %><br/>
                                    <strong>Students:</strong> <%= reportData.students.length %><br/>
                                    <strong>Sessions:</strong> <%= reportData.sessions.length %>
                                <% } else if (reportType === 'sf4') { %>
                                    <strong>Month/Year:</strong> <%= reportData.report_info.month_name %> <%= reportData.report_info.year %><br/>
                                    <strong>Grade Level:</strong> <%= reportData.report_info.grade_level || 'All Levels' %><br/>
                                    <strong>Section:</strong> <%= reportData.report_info.section || 'All Sections' %><br/>
                                    <strong>Students:</strong> <%= reportData.students.length %><br/>
                                    <strong>At Risk:</strong> <%= reportData.statistics.at_risk_students %>
                                <% } %>
                            </div>
                        </div>
                        <div class="col-md-4 text-right">
                            <div class="zoom-controls">
                                <label class="small text-muted">Zoom:</label>
                                <button type="button" class="btn btn-sm btn-outline-secondary" onclick="zoomOut()">
                                    <i class="fas fa-search-minus"></i>
                                </button>
                                <span id="zoomLevel" class="mx-2 small">100%</span>
                                <button type="button" class="btn btn-sm btn-outline-secondary" onclick="zoomIn()">
                                    <i class="fas fa-search-plus"></i>
                                </button>
                                <button type="button" class="btn btn-sm btn-outline-secondary ml-2" onclick="resetZoom()">
                                    <i class="fas fa-expand-arrows-alt"></i>
                                </button>
                            </div>
                        </div>
                    </div>
                </div>

                <div class="preview-content position-relative">
                    <div id="loadingOverlay" class="loading-overlay" style="display: none;">
                        <div class="text-center">
                            <div class="spinner-border text-primary" role="status">
                                <span class="sr-only">Loading...</span>
                            </div>
                            <p class="mt-3">Generating preview...</p>
                        </div>
                    </div>
                    
                    <div id="previewContent" style="transform-origin: top left; transition: transform 0.3s;">
                        <%- htmlContent %>
                    </div>
                </div>

                <div class="preview-actions">
                    <div class="report-info">
                        <small class="text-muted">
                            <i class="fas fa-info-circle"></i>
                            Preview generated on <%= new Date().toLocaleString() %>
                        </small>
                    </div>
                    <div>
                        <form method="POST" action="/reports/<%= reportType %>/export" style="display: inline;">
                            <% 
                            // Pass through the original parameters
                            const params = reportType === 'sf2' 
                                ? { date: reportData.report_info.date, gradeLevel: reportData.report_info.grade_level, section: reportData.report_info.section }
                                : { month: reportData.report_info.month, year: reportData.report_info.year, gradeLevel: reportData.report_info.grade_level, section: reportData.report_info.section };
                            
                            Object.entries(params).forEach(([key, value]) => {
                                if (value) {
                            %>
                                <input type="hidden" name="<%= key %>" value="<%= value %>">
                            <% 
                                }
                            });
                            %>
                            
                            <button type="submit" name="format" value="pdf" class="btn btn-pdf btn-export">
                                <i class="fas fa-file-pdf"></i>
                                Export PDF
                            </button>
                            <button type="submit" name="format" value="excel" class="btn btn-excel btn-export">
                                <i class="fas fa-file-excel"></i>
                                Export Excel
                            </button>
                        </form>
                        
                        <button type="button" class="btn btn-outline-secondary" onclick="regeneratePreview()">
                            <i class="fas fa-sync-alt"></i>
                            Regenerate
                        </button>
                    </div>
                </div>
            </div>

            <!-- Tips Section -->
            <div class="alert alert-info">
                <h6><i class="fas fa-lightbulb"></i> Preview Tips:</h6>
                <ul class="mb-0">
                    <li>Use the zoom controls to get a better view of the report details</li>
                    <li>The PDF export will maintain the exact formatting shown in the preview</li>
                    <li>Excel export provides an editable spreadsheet version of the data</li>
                    <li>Click "Edit Parameters" to modify the report criteria and regenerate</li>
                </ul>
            </div>
        </div>
    </div>
</div>

<script>
let currentZoom = 1;

function zoomIn() {
    currentZoom = Math.min(currentZoom + 0.1, 2);
    updateZoom();
}

function zoomOut() {
    currentZoom = Math.max(currentZoom - 0.1, 0.5);
    updateZoom();
}

function resetZoom() {
    currentZoom = 1;
    updateZoom();
}

function updateZoom() {
    const content = document.getElementById('previewContent');
    content.style.transform = `scale(${currentZoom})`;
    document.getElementById('zoomLevel').textContent = Math.round(currentZoom * 100) + '%';
}

function regeneratePreview() {
    const overlay = document.getElementById('loadingOverlay');
    overlay.style.display = 'flex';
    
    // Simulate regeneration delay
    setTimeout(() => {
        overlay.style.display = 'none';
        // In a real implementation, this would reload the preview content
        alert('Preview regenerated successfully!');
    }, 2000);
}

// Handle export form submissions
document.addEventListener('DOMContentLoaded', function() {
    const forms = document.querySelectorAll('form[action*="/export"]');
    forms.forEach(form => {
        form.addEventListener('submit', function() {
            const overlay = document.getElementById('loadingOverlay');
            overlay.style.display = 'flex';
            
            // Hide overlay after a delay (the download should start)
            setTimeout(() => {
                overlay.style.display = 'none';
            }, 3000);
        });
    });
});

// Print functionality
function printPreview() {
    const content = document.getElementById('previewContent').innerHTML;
    const printWindow = window.open('', '_blank');
    printWindow.document.write(`
        <html>
            <head>
                <title>Report Preview</title>
                <style>
                    body { font-family: 'Times New Roman', serif; margin: 0; padding: 20px; }
                    table { border-collapse: collapse; width: 100%; }
                    th, td { border: 1px solid #000; padding: 4px; text-align: center; }
                    .present { background-color: #90EE90; }
                    .late { background-color: #FFE4B5; }
                    .absent { background-color: #FFB6C1; }
                    .at-risk { background-color: #FFB6C1; }
                </style>
            </head>
            <body>
                ${content}
            </body>
        </html>
    `);
    printWindow.document.close();
    printWindow.print();
}
</script>
