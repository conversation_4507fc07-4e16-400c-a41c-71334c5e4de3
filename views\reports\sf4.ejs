<%- include('../layout', { 
    title: 'SF4 School Register',
    additionalCSS: `
        <style>
            .form-section {
                background: white;
                border-radius: 8px;
                box-shadow: 0 2px 4px rgba(0,0,0,0.1);
                padding: 25px;
                margin-bottom: 20px;
            }
            .section-title {
                font-size: 18px;
                font-weight: bold;
                margin-bottom: 20px;
                color: #333;
                border-bottom: 2px solid #007bff;
                padding-bottom: 10px;
            }
            .form-group label {
                font-weight: 600;
                color: #555;
            }
            .btn-generate {
                background: #17a2b8;
                border-color: #17a2b8;
                padding: 12px 30px;
                font-weight: 600;
            }
            .btn-generate:hover {
                background: #138496;
                border-color: #117a8b;
            }
            .month-year-group {
                display: flex;
                gap: 10px;
            }
            .month-year-group .form-control {
                flex: 1;
            }
        </style>
    `
}) %>

<div class="container-fluid">
    <div class="row">
        <div class="col-12">
            <div class="d-flex justify-content-between align-items-center mb-4">
                <h1 class="h3">
                    <i class="fas fa-book"></i>
                    SF4 School Register
                </h1>
                <a href="/reports" class="btn btn-outline-secondary">
                    <i class="fas fa-arrow-left"></i>
                    Back to Dashboard
                </a>
            </div>

            <!-- Report Generation Form -->
            <div class="form-section">
                <div class="section-title">
                    <i class="fas fa-cog"></i>
                    Report Parameters
                </div>

                <form id="sf4Form" method="POST" action="/reports/sf4/generate">
                    <div class="row">
                        <div class="col-md-6">
                            <div class="form-group">
                                <label>Report Period *</label>
                                <div class="month-year-group">
                                    <select class="form-control" id="month" name="month" required>
                                        <option value="">Select Month</option>
                                        <option value="1">January</option>
                                        <option value="2">February</option>
                                        <option value="3">March</option>
                                        <option value="4">April</option>
                                        <option value="5">May</option>
                                        <option value="6">June</option>
                                        <option value="7">July</option>
                                        <option value="8">August</option>
                                        <option value="9">September</option>
                                        <option value="10">October</option>
                                        <option value="11">November</option>
                                        <option value="12">December</option>
                                    </select>
                                    <select class="form-control" id="year" name="year" required>
                                        <option value="">Year</option>
                                        <!-- Years will be populated by JavaScript -->
                                    </select>
                                </div>
                                <small class="form-text text-muted">Select the month and year for the report</small>
                            </div>
                        </div>
                        <div class="col-md-3">
                            <div class="form-group">
                                <label for="gradeLevel">Grade Level</label>
                                <select class="form-control" id="gradeLevel" name="gradeLevel">
                                    <option value="">All Grade Levels</option>
                                    <% gradeLevels.forEach(grade => { %>
                                        <option value="<%= grade %>"><%= grade %></option>
                                    <% }); %>
                                </select>
                                <small class="form-text text-muted">Filter by grade level</small>
                            </div>
                        </div>
                        <div class="col-md-3">
                            <div class="form-group">
                                <label for="section">Section</label>
                                <select class="form-control" id="section" name="section">
                                    <option value="">All Sections</option>
                                    <% sections.forEach(section => { %>
                                        <option value="<%= section %>"><%= section %></option>
                                    <% }); %>
                                </select>
                                <small class="form-text text-muted">Filter by section</small>
                            </div>
                        </div>
                    </div>

                    <div class="row">
                        <div class="col-md-6">
                            <div class="form-group">
                                <label for="format">Export Format</label>
                                <select class="form-control" id="format" name="format">
                                    <option value="preview">Preview First</option>
                                    <option value="pdf">PDF (Recommended)</option>
                                    <option value="excel">Excel Spreadsheet</option>
                                </select>
                                <small class="form-text text-muted">Choose output format</small>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="form-group">
                                <label>&nbsp;</label>
                                <div class="export-buttons">
                                    <button type="submit" name="format" value="preview" class="btn btn-info btn-block mb-2">
                                        <i class="fas fa-eye"></i>
                                        Preview Report
                                    </button>
                                    <div class="row">
                                        <div class="col-6">
                                            <button type="submit" name="format" value="pdf" class="btn btn-danger btn-block">
                                                <i class="fas fa-file-pdf"></i>
                                                Export PDF
                                            </button>
                                        </div>
                                        <div class="col-6">
                                            <button type="submit" name="format" value="excel" class="btn btn-success btn-block">
                                                <i class="fas fa-file-excel"></i>
                                                Export Excel
                                            </button>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </form>
            </div>

            <!-- Quick Actions -->
            <div class="form-section">
                <div class="section-title">
                    <i class="fas fa-bolt"></i>
                    Quick Actions
                </div>
                <div class="row">
                    <div class="col-md-3">
                        <button class="btn btn-outline-primary btn-block" onclick="setCurrentMonth()">
                            <i class="fas fa-calendar-alt"></i>
                            Current Month
                        </button>
                    </div>
                    <div class="col-md-3">
                        <button class="btn btn-outline-primary btn-block" onclick="setLastMonth()">
                            <i class="fas fa-calendar-minus"></i>
                            Last Month
                        </button>
                    </div>
                    <div class="col-md-3">
                        <button class="btn btn-outline-secondary btn-block" onclick="showComingSoon()">
                            <i class="fas fa-chart-line"></i>
                            Semester Report
                        </button>
                    </div>
                    <div class="col-md-3">
                        <button class="btn btn-outline-info btn-block" onclick="showComingSoon()">
                            <i class="fas fa-download"></i>
                            Annual Report
                        </button>
                    </div>
                </div>
            </div>

            <!-- Information Section -->
            <div class="form-section">
                <div class="section-title">
                    <i class="fas fa-info-circle"></i>
                    About SF4 Reports
                </div>
                <div class="row">
                    <div class="col-md-6">
                        <h6>What is SF4?</h6>
                        <p class="text-muted">
                            School Form 4 (SF4) is the official DepEd form for monthly learner's movement and attendance. 
                            It provides a comprehensive overview of student enrollment status, attendance patterns, 
                            and identifies students at risk of dropping out.
                        </p>
                        
                        <h6>Report Contents:</h6>
                        <ul class="text-muted">
                            <li>Student enrollment information</li>
                            <li>Monthly attendance summaries</li>
                            <li>Attendance rates and patterns</li>
                            <li>Student status tracking</li>
                            <li>At-risk student identification</li>
                            <li>Enrollment statistics</li>
                        </ul>
                    </div>
                    <div class="col-md-6">
                        <h6>Key Features:</h6>
                        <ul class="text-muted">
                            <li><strong>Monthly Tracking:</strong> Comprehensive monthly attendance data</li>
                            <li><strong>Risk Assessment:</strong> Automatic identification of at-risk students</li>
                            <li><strong>Status Monitoring:</strong> Track active, transferred, and graduated students</li>
                            <li><strong>Statistical Analysis:</strong> Overall class and school statistics</li>
                        </ul>
                        
                        <h6>At-Risk Indicators:</h6>
                        <ul class="text-muted">
                            <li>More than 10 absent days in a month</li>
                            <li>Attendance rate below 50%</li>
                            <li>Extended periods without attendance</li>
                            <li>Irregular attendance patterns</li>
                        </ul>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Loading Modal -->
<div class="modal fade" id="loadingModal" tabindex="-1" role="dialog" aria-hidden="true">
    <div class="modal-dialog modal-dialog-centered" role="document">
        <div class="modal-content">
            <div class="modal-body text-center">
                <div class="spinner-border text-info" role="status">
                    <span class="sr-only">Loading...</span>
                </div>
                <p class="mt-3">Generating SF4 report...</p>
                <small class="text-muted">Processing monthly data...</small>
            </div>
        </div>
    </div>
</div>

<script>
document.addEventListener('DOMContentLoaded', function() {
    // Populate year dropdown
    const yearSelect = document.getElementById('year');
    const currentYear = new Date().getFullYear();
    
    for (let year = currentYear; year >= currentYear - 5; year--) {
        const option = document.createElement('option');
        option.value = year;
        option.textContent = year;
        yearSelect.appendChild(option);
    }
    
    // Set default to current month and year
    const now = new Date();
    document.getElementById('month').value = now.getMonth() + 1;
    document.getElementById('year').value = currentYear;

    // Handle form submission
    document.getElementById('sf4Form').addEventListener('submit', function(e) {
        const format = document.getElementById('format').value;
        
        if (format === 'pdf' || format === 'excel') {
            // Show loading modal for direct downloads
            $('#loadingModal').modal('show');
            
            // Hide modal after a delay (the download should start)
            setTimeout(() => {
                $('#loadingModal').modal('hide');
            }, 5000);
        }
    });
});

function setCurrentMonth() {
    const now = new Date();
    document.getElementById('month').value = now.getMonth() + 1;
    document.getElementById('year').value = now.getFullYear();
}

function setLastMonth() {
    const now = new Date();
    now.setMonth(now.getMonth() - 1);
    document.getElementById('month').value = now.getMonth() + 1;
    document.getElementById('year').value = now.getFullYear();
}

function showComingSoon() {
    alert('This feature is coming soon! Please check back later.');
}

// Handle URL parameters
const urlParams = new URLSearchParams(window.location.search);
if (urlParams.get('month')) {
    document.getElementById('month').value = urlParams.get('month');
}
if (urlParams.get('year')) {
    document.getElementById('year').value = urlParams.get('year');
}
if (urlParams.get('gradeLevel')) {
    document.getElementById('gradeLevel').value = urlParams.get('gradeLevel');
}
if (urlParams.get('section')) {
    document.getElementById('section').value = urlParams.get('section');
}
</script>
