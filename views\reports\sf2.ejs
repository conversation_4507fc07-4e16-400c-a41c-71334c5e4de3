<%- include('../layout', { 
    title: 'SF2 Daily Attendance Report',
    additionalCSS: `
        <style>
            .form-section {
                background: white;
                border-radius: 8px;
                box-shadow: 0 2px 4px rgba(0,0,0,0.1);
                padding: 25px;
                margin-bottom: 20px;
            }
            .section-title {
                font-size: 18px;
                font-weight: bold;
                margin-bottom: 20px;
                color: #333;
                border-bottom: 2px solid #007bff;
                padding-bottom: 10px;
            }
            .form-group label {
                font-weight: 600;
                color: #555;
            }
            .btn-generate {
                background: #28a745;
                border-color: #28a745;
                padding: 12px 30px;
                font-weight: 600;
            }
            .btn-generate:hover {
                background: #218838;
                border-color: #1e7e34;
            }
            .preview-section {
                background: #f8f9fa;
                border-radius: 8px;
                padding: 20px;
                margin-top: 20px;
                display: none;
            }
            .export-buttons {
                margin-top: 15px;
            }
            .export-buttons .btn {
                margin-right: 10px;
                margin-bottom: 10px;
            }
        </style>
    `
}) %>

<div class="container-fluid">
    <div class="row">
        <div class="col-12">
            <div class="d-flex justify-content-between align-items-center mb-4">
                <h1 class="h3">
                    <i class="fas fa-calendar-day"></i>
                    SF2 Daily Attendance Report
                </h1>
                <a href="/reports" class="btn btn-outline-secondary">
                    <i class="fas fa-arrow-left"></i>
                    Back to Dashboard
                </a>
            </div>

            <!-- Report Generation Form -->
            <div class="form-section">
                <div class="section-title">
                    <i class="fas fa-cog"></i>
                    Report Parameters
                </div>

                <form id="sf2Form" method="POST" action="/reports/sf2/generate">
                    <div class="row">
                        <div class="col-md-4">
                            <div class="form-group">
                                <label for="date">Report Date *</label>
                                <input type="date" class="form-control" id="date" name="date" required>
                                <small class="form-text text-muted">Select the date for the attendance report</small>
                            </div>
                        </div>
                        <div class="col-md-4">
                            <div class="form-group">
                                <label for="gradeLevel">Grade Level</label>
                                <select class="form-control" id="gradeLevel" name="gradeLevel">
                                    <option value="">All Grade Levels</option>
                                    <% gradeLevels.forEach(grade => { %>
                                        <option value="<%= grade %>"><%= grade %></option>
                                    <% }); %>
                                </select>
                                <small class="form-text text-muted">Filter by specific grade level (optional)</small>
                            </div>
                        </div>
                        <div class="col-md-4">
                            <div class="form-group">
                                <label for="section">Section</label>
                                <select class="form-control" id="section" name="section">
                                    <option value="">All Sections</option>
                                    <% sections.forEach(section => { %>
                                        <option value="<%= section %>"><%= section %></option>
                                    <% }); %>
                                </select>
                                <small class="form-text text-muted">Filter by specific section (optional)</small>
                            </div>
                        </div>
                    </div>

                    <div class="row">
                        <div class="col-md-6">
                            <div class="form-group">
                                <label for="format">Export Format</label>
                                <select class="form-control" id="format" name="format">
                                    <option value="preview">Preview First</option>
                                    <option value="pdf">PDF (Recommended)</option>
                                    <option value="excel">Excel Spreadsheet</option>
                                </select>
                                <small class="form-text text-muted">Choose output format</small>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="form-group">
                                <label>&nbsp;</label>
                                <div class="export-buttons">
                                    <button type="submit" name="format" value="preview" class="btn btn-info btn-block mb-2">
                                        <i class="fas fa-eye"></i>
                                        Preview Report
                                    </button>
                                    <div class="row">
                                        <div class="col-6">
                                            <button type="submit" name="format" value="pdf" class="btn btn-danger btn-block">
                                                <i class="fas fa-file-pdf"></i>
                                                Export PDF
                                            </button>
                                        </div>
                                        <div class="col-6">
                                            <button type="submit" name="format" value="excel" class="btn btn-success btn-block">
                                                <i class="fas fa-file-excel"></i>
                                                Export Excel
                                            </button>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </form>
            </div>

            <!-- Quick Actions -->
            <div class="form-section">
                <div class="section-title">
                    <i class="fas fa-bolt"></i>
                    Quick Actions
                </div>
                <div class="row">
                    <div class="col-md-3">
                        <button class="btn btn-outline-primary btn-block" onclick="setToday()">
                            <i class="fas fa-calendar-day"></i>
                            Today's Report
                        </button>
                    </div>
                    <div class="col-md-3">
                        <button class="btn btn-outline-primary btn-block" onclick="setYesterday()">
                            <i class="fas fa-calendar-minus"></i>
                            Yesterday's Report
                        </button>
                    </div>
                    <div class="col-md-3">
                        <button class="btn btn-outline-secondary btn-block" onclick="showComingSoon()">
                            <i class="fas fa-calendar-week"></i>
                            Weekly Summary
                        </button>
                    </div>
                    <div class="col-md-3">
                        <button class="btn btn-outline-info btn-block" onclick="showComingSoon()">
                            <i class="fas fa-download"></i>
                            Bulk Download
                        </button>
                    </div>
                </div>
            </div>

            <!-- Information Section -->
            <div class="form-section">
                <div class="section-title">
                    <i class="fas fa-info-circle"></i>
                    About SF2 Reports
                </div>
                <div class="row">
                    <div class="col-md-6">
                        <h6>What is SF2?</h6>
                        <p class="text-muted">
                            School Form 2 (SF2) is the official DepEd form for daily attendance reporting. 
                            It provides a comprehensive view of student attendance for a specific date, 
                            including individual session attendance and daily summaries.
                        </p>
                        
                        <h6>Report Contents:</h6>
                        <ul class="text-muted">
                            <li>Student names and LRN numbers</li>
                            <li>Attendance status for each class session</li>
                            <li>Daily attendance summary (Present, Late, Absent)</li>
                            <li>Overall class statistics</li>
                            <li>Teacher and principal signature areas</li>
                        </ul>
                    </div>
                    <div class="col-md-6">
                        <h6>Export Options:</h6>
                        <ul class="text-muted">
                            <li><strong>PDF:</strong> Official DepEd-compliant format for printing and submission</li>
                            <li><strong>Excel:</strong> Editable spreadsheet for data manipulation and analysis</li>
                            <li><strong>Preview:</strong> Review the report before final export</li>
                        </ul>
                        
                        <h6>Filtering Options:</h6>
                        <ul class="text-muted">
                            <li><strong>Date:</strong> Required - select the specific date for the report</li>
                            <li><strong>Grade Level:</strong> Optional - filter by specific grade level</li>
                            <li><strong>Section:</strong> Optional - filter by specific section</li>
                        </ul>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Loading Modal -->
<div class="modal fade" id="loadingModal" tabindex="-1" role="dialog" aria-hidden="true">
    <div class="modal-dialog modal-dialog-centered" role="document">
        <div class="modal-content">
            <div class="modal-body text-center">
                <div class="spinner-border text-primary" role="status">
                    <span class="sr-only">Loading...</span>
                </div>
                <p class="mt-3">Generating SF2 report...</p>
                <small class="text-muted">This may take a few moments</small>
            </div>
        </div>
    </div>
</div>

<script>
document.addEventListener('DOMContentLoaded', function() {
    // Set default date to today
    const today = new Date().toISOString().split('T')[0];
    document.getElementById('date').value = today;

    // Handle form submission
    document.getElementById('sf2Form').addEventListener('submit', function(e) {
        const format = document.getElementById('format').value;
        
        if (format === 'pdf' || format === 'excel') {
            // Show loading modal for direct downloads
            $('#loadingModal').modal('show');
            
            // Hide modal after a delay (the download should start)
            setTimeout(() => {
                $('#loadingModal').modal('hide');
            }, 3000);
        }
    });
});

function setToday() {
    const today = new Date().toISOString().split('T')[0];
    document.getElementById('date').value = today;
}

function setYesterday() {
    const yesterday = new Date();
    yesterday.setDate(yesterday.getDate() - 1);
    document.getElementById('date').value = yesterday.toISOString().split('T')[0];
}

function showComingSoon() {
    alert('This feature is coming soon! Please check back later.');
}

// Handle URL parameters
const urlParams = new URLSearchParams(window.location.search);
if (urlParams.get('date')) {
    document.getElementById('date').value = urlParams.get('date');
}
if (urlParams.get('gradeLevel')) {
    document.getElementById('gradeLevel').value = urlParams.get('gradeLevel');
}
if (urlParams.get('section')) {
    document.getElementById('section').value = urlParams.get('section');
}
</script>
